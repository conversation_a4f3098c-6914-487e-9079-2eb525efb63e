<?php
require_once 'includes/header.php';
require_once 'includes/user_helpers.php';

// Load modern action menu CSS
echo '<link rel="stylesheet" href="assets/css/modern-action-menu.css">';
echo '<link rel="stylesheet" href="assets/css/stats-grid.css">';
echo '<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">';
echo '<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap5.min.css">';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Check if user is logged in
if (!$auth->isLoggedIn()) {
    Utilities::setFlashMessage('error', 'You must be logged in to access this page.');
    Utilities::redirect('login.php');
}

// Check if user has admin role for full access
$isAdmin = $auth->hasRole('admin') || $auth->hasRole('super_admin');
$isSuperAdmin = $auth->hasRole('super_admin');
$isStaff = $auth->hasRole('staff');

// Get current admin role and ID
$currentAdminRole = $auth->getUserRole();
$currentAdminId = $auth->getUserId();

// Handle user deletion (admin only)
if ($isAdmin && isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $userId = (int)$_GET['delete'];

    // Don't allow deleting the admin user (ID 1)
    if ($userId == 1) {
        Utilities::setFlashMessage('error', 'Cannot delete the admin user.');
        Utilities::redirect('users.php');
    }

    $deleteQuery = "DELETE FROM users WHERE id = ? AND id != 1";
    $stmt = $conn->prepare($deleteQuery);
    $stmt->bind_param("i", $userId);

    if ($stmt->execute()) {
        Utilities::setFlashMessage('success', 'User has been deleted successfully.');
    } else {
        Utilities::setFlashMessage('error', 'Failed to delete user.');
    }

    Utilities::redirect('users.php');
}

// Handle user status toggle (admin only)
if ($isAdmin && isset($_GET['action']) && isset($_GET['id']) && is_numeric($_GET['id'])) {
    $action = $_GET['action'];
    $userId = (int)$_GET['id'];

    // Don't allow deactivating the admin user (ID 1)
    if ($userId == 1 && $action == 'deactivate') {
        Utilities::setFlashMessage('error', 'Cannot deactivate the admin user.');
        Utilities::redirect('users.php');
    }

    if ($action == 'activate') {
        $query = "UPDATE users SET is_active = 1 WHERE id = ? AND id != 1";
        $successMessage = "User activated successfully.";
        $errorMessage = "Failed to activate user.";
    } elseif ($action == 'deactivate') {
        $query = "UPDATE users SET is_active = 0 WHERE id = ? AND id != 1";
        $successMessage = "User deactivated successfully.";
        $errorMessage = "Failed to deactivate user.";
    } else {
        Utilities::redirect('users.php');
    }

    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $userId);

    if ($stmt->execute()) {
        Utilities::setFlashMessage('success', $successMessage);
    } else {
        Utilities::setFlashMessage('error', $errorMessage);
    }

    Utilities::redirect('users.php');
}

// Handle bulk actions (admin only)
if ($isAdmin && isset($_POST['bulk_action']) && isset($_POST['user_ids']) && !empty($_POST['bulk_action'])) {
    $action = $_POST['bulk_action'];
    $userIds = $_POST['user_ids'] ?? [];

    if (!empty($userIds)) {
        $successCount = 0;
        $errorCount = 0;

        foreach ($userIds as $userId) {
            if (!is_numeric($userId)) continue;

            // Skip admin user for certain actions
            if ($userId == 1 && ($action == 'delete' || $action == 'deactivate')) {
                $errorCount++;
                continue;
            }

            switch ($action) {
                case 'delete':
                    $query = "DELETE FROM users WHERE id = ? AND id != 1";
                    break;
                case 'activate':
                    $query = "UPDATE users SET is_active = 1 WHERE id = ? AND id != 1";
                    break;
                case 'deactivate':
                    $query = "UPDATE users SET is_active = 0 WHERE id = ? AND id != 1";
                    break;
                default:
                    continue 2; // Skip to next user ID
            }

            $stmt = $conn->prepare($query);
            $stmt->bind_param("i", $userId);

            if ($stmt->execute()) {
                $successCount++;
            } else {
                $errorCount++;
            }
        }

        if ($successCount > 0) {
            $actionText = ucfirst(str_replace(['_', '-'], ' ', $action));
            Utilities::setFlashMessage('success', "$actionText action completed successfully for $successCount user(s).");
        }

        if ($errorCount > 0) {
            Utilities::setFlashMessage('error', "Failed to process $errorCount user(s).");
        }

        Utilities::redirect('users.php');
    }
}

// Get filter parameters
$search = '';
if (isset($_GET['user_search']) && !empty($_GET['user_search'])) {
    // If the value is numeric, treat as user ID and redirect
    if (is_numeric($_GET['user_search'])) {
        header('Location: user_view.php?id=' . intval($_GET['user_search']));
        exit;
    }
    $search = Utilities::sanitizeInput($_GET['user_search']);
} elseif (isset($_GET['search']) && !empty($_GET['search'])) {
    $search = Utilities::sanitizeInput($_GET['search']);
}
$status = isset($_GET['status']) ? Utilities::sanitizeInput($_GET['status']) : '';
$verification = isset($_GET['verification']) ? Utilities::sanitizeInput($_GET['verification']) : '';
$dateRange = isset($_GET['date_range']) ? Utilities::sanitizeInput($_GET['date_range']) : '';
$sort = isset($_GET['sort']) ? Utilities::sanitizeInput($_GET['sort']) : 'created_at';
$order = isset($_GET['order']) ? Utilities::sanitizeInput($_GET['order']) : 'DESC';
$staffId = isset($_GET['staff_id']) ? (int)$_GET['staff_id'] : '';

// Get all staff members for the filter dropdown (for super admins)
$staffMembers = [];
if ($isSuperAdmin) {
    $staffMembers = getAllStaffMembers($conn);
}

// Create filters array for the helper function
$filters = [
    'search' => $search,
    'status' => $status,
    'verification' => $verification,
    'date_range' => $dateRange,
    'sort' => $sort,
    'order' => $order,
    'staff_id' => $staffId
];

// Add pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 15;
$offset = ($page - 1) * $limit;

// Get total count for pagination
$totalUsers = getUsersWithFilters($conn, $auth, $filters, true);
$totalPages = ceil($totalUsers / $limit);

// Get users with pagination
$users = getUsersWithFilters($conn, $auth, $filters, false, $limit, $offset);

// After fetching $users = getUsersWithFilters(...)
if (isset($_GET['user_search']) && !empty($_GET['user_search']) && count($users) === 1) {
    // If only one user matches the search, redirect to their profile
    header('Location: user_view.php?id=' . $users[0]['id']);
    exit;
}

// Get user statistics
$userStats = getUserStatistics($conn, $auth, $filters);

// Calculate Active This Month
$activeThisMonth = 0;
$activeThisMonthPercent = 0;
if (!empty($users)) {
    $now = new DateTime();
    $monthAgo = (clone $now)->modify('-30 days');
    $activeThisMonth = 0;
    foreach ($users as $user) {
        if (!empty($user['last_login']) && (new DateTime($user['last_login'])) >= $monthAgo) {
            $activeThisMonth++;
        }
    }
    $activeThisMonthPercent = $userStats['total'] > 0 ? round(($activeThisMonth / $userStats['total']) * 100) : 0;
}

// Debug the result
error_log("Number of users found: " . count($users));
?>

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <div>
        <h1 class="h3 mb-0 text-gray-800 font-weight-bold">App Users</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb bg-transparent p-0 mb-0">
                <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
                <li class="breadcrumb-item active" aria-current="page">App Users</li>
            </ol>
        </nav>
    </div>
</div>

<style>
/* Top Bar Styles */
@media screen and (max-width: 767px) {
    .d-sm-flex {
        padding: 1rem;
        background: #fff;
        border-radius: 16px;
        box-shadow: 0 2px 12px rgba(0,0,0,0.04);
        margin-bottom: 1.5rem;
    }

    .h3 {
        font-size: 1.5rem;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .breadcrumb {
        margin: 0;
        padding: 0;
    }

    .breadcrumb-item {
        font-size: 0.875rem;
        color: #6c757d;
    }

    .breadcrumb-item a {
        color: #27ae60;
        text-decoration: none;
        font-weight: 500;
    }

    .breadcrumb-item.active {
        color: #2c3e50;
        font-weight: 500;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        content: "›";
        color: #6c757d;
        font-size: 1.1rem;
        line-height: 1;
        padding: 0 0.5rem;
    }
}

.leftside-flash {
    position: fixed;
    top: 50%;
    left: 220px; /* Sidebar width */
    transform: translateY(-50%);
    min-width: 180px;
    max-width: 320px;
    z-index: 3000;
    border-radius: 0 8px 8px 0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    background: #fff;
    padding: 0;
    margin: 0;
    border: none;
    pointer-events: auto;
}
.leftside-flash .alert {
    border-radius: 0 8px 8px 0;
    margin-bottom: 0;
    box-shadow: none;
    background: #fff;
    border-left: 4px solid #27ae60;
    color: #222;
    font-size: 0.98rem;
    padding: 0.6rem 1.1rem 0.6rem 1rem;
    min-width: 180px;
    max-width: 320px;
    line-height: 1.3;
}
.leftside-flash .alert-danger { border-left-color: #e74c3c; }
.leftside-flash .alert-warning { border-left-color: #f1c40f; }
.leftside-flash .alert-info { border-left-color: #3498db; }
.leftside-flash .alert-success { border-left-color: #27ae60; }
.leftside-flash .btn-close { right: 0.7rem; top: 0.7rem; }
@media (max-width: 900px) {
    .leftside-flash { left: 0; top: 0; transform: none; width: 100vw; border-radius: 0; }
    .leftside-flash .alert { border-radius: 0; min-width: 100vw; max-width: 100vw; }
}
</style>

<div class="leftside-flash">
<?php Utilities::displayFlashMessages(); ?>
</div>

<?php if ($isAdmin || $isStaff): ?>
<!-- Floating Action Button (FAB) for Add New User -->
<a href="user_add.php<?php echo $isStaff ? '?assigned_staff_id=' . $currentAdminId : ''; ?>" class="fab-add-user" title="Add New User">
    <i class="fas fa-plus"></i>
</a>
<style>
.fab-add-user {
    position: fixed;
    bottom: 32px;
    right: 32px;
    width: 60px;
    height: 60px;
    background: #27ae60;
    color: #fff;
    border-radius: 50%;
    box-shadow: 0 4px 16px rgba(39, 174, 96, 0.18);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    z-index: 1050;
    transition: background 0.2s, box-shadow 0.2s;
    text-decoration: none;
}
.fab-add-user:hover, .fab-add-user:focus {
    background: #219150;
    color: #fff;
    box-shadow: 0 8px 24px rgba(39, 174, 96, 0.28);
    text-decoration: none;
}
@media (max-width: 600px) {
    .fab-add-user {
        width: 48px;
        height: 48px;
        font-size: 1.5rem;
        bottom: 20px;
        right: 20px;
    }
}
</style>
<?php endif; ?>

<?php if ($isAdmin || $isStaff): ?>
<div class="stats-grid mb-4">
    <div class="dashboard-card minimalist-card">
        <div class="d-flex align-items-center gap-3">
            <i class="fas fa-users minimalist-icon"></i>
                <div>
                <div class="minimalist-label">Total Users</div>
                <div class="minimalist-value"><?php echo number_format($userStats['total']); ?></div>
                <div class="minimalist-desc">
                <?php if ($isStaff): ?>Users assigned to you<?php elseif ($isSuperAdmin && !empty($staffId)): ?>Users for selected staff<?php else: ?>All app users<?php endif; ?>
            </div>
        </div>
    </div>
                    </div>
    <div class="dashboard-card minimalist-card">
        <div class="d-flex align-items-center gap-3">
            <i class="fas fa-user-check minimalist-icon"></i>
                <div>
                <div class="minimalist-label">Active</div>
                <div class="minimalist-value"><?php echo number_format($userStats['active']); ?></div>
                <div class="minimalist-desc"><?php echo $userStats['active_percentage']; ?>% active</div>
                </div>
                </div>
            </div>
    <div class="dashboard-card minimalist-card">
        <div class="d-flex align-items-center gap-3">
            <i class="fas fa-bolt minimalist-icon"></i>
                <div>
                <div class="minimalist-label">Active This Month</div>
                <div class="minimalist-value"><?php echo number_format($activeThisMonth); ?></div>
                <div class="minimalist-desc"><?php echo $activeThisMonthPercent; ?>% of all users</div>
                    </div>
                </div>
                </div>
    <div class="dashboard-card minimalist-card">
        <div class="d-flex align-items-center gap-3">
            <i class="fas fa-user-plus minimalist-icon"></i>
                <div>
                <div class="minimalist-label">New (7d)</div>
                <div class="minimalist-value"><?php echo number_format($userStats['new']); ?></div>
                <div class="minimalist-desc">Last 7 days</div>
                </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Filters and Search -->
<?php if ($isAdmin): ?>
<style>
.filters-minimal-card {
    border: none !important;
    box-shadow: none !important;
    background: #f8f9fa;
    border-radius: 10px;
    padding: 0.7rem 1.2rem 0.7rem 1.2rem;
    margin-bottom: 1.2rem;
}
.filters-minimal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem 0 0.5rem 0;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
    position: sticky;
    top: 0;
    z-index: 10;
}
.filters-minimal-header h6 {
    font-size: 1rem;
    font-weight: 600;
    color: #27ae60;
    margin: 0;
    display: flex;
    align-items: center;
}
.filters-minimal-header i {
    font-size: 1.1rem;
    margin-right: 0.5rem;
}
.filters-minimal-form .form-label {
    font-size: 0.92rem;
    color: #888;
    margin-bottom: 0.2rem;
}
.filters-minimal-form .form-control, .filters-minimal-form .form-select {
    font-size: 0.95rem;
    padding: 0.35rem 0.7rem;
    border-radius: 6px;
    border: 1px solid #e0e0e0;
    background: #fff;
}
.filters-minimal-form .input-group-text {
    background: #f3f3f3;
    border: 1px solid #e0e0e0;
    font-size: 1rem;
}
.filters-minimal-form .btn {
    font-size: 0.95rem;
    border-radius: 6px;
    padding: 0.35rem 1.1rem;
}
.filters-minimal-form .btn-success {
    background: #27ae60 !important;
    border: none !important;
}
.filters-minimal-form .btn-success:hover {
    background: #219150 !important;
}
</style>
<div class="card filters-minimal-card mb-4">
    <div class="filters-minimal-header">
        <h6><i class="fas fa-filter me-2"></i> Filters</h6>
        <button class="btn btn-sm btn-link text-primary p-0" type="button" data-bs-toggle="collapse" data-bs-target="#filtersCollapse" aria-expanded="false" aria-controls="filtersCollapse" style="font-size:1.1rem;"><i class="fas fa-chevron-down"></i></button>
    </div>
    <div class="collapse" id="filtersCollapse">
        <div class="card-body pb-2 pt-3 filters-minimal-form">
            <form action="users.php" method="get" class="row g-2 align-items-end">
                <div class="col-lg-2 col-md-3 col-sm-6">
                    <div class="form-group mb-2">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All</option>
                            <option value="active" <?php echo $status === 'active' ? 'selected' : ''; ?>>Active</option>
                            <option value="inactive" <?php echo $status === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                        </select>
                    </div>
                </div>
                <div class="col-lg-2 col-md-3 col-sm-6">
                    <div class="form-group mb-2">
                        <label for="verification" class="form-label">Verification</label>
                        <select class="form-select" id="verification" name="verification">
                            <option value="">All</option>
                            <option value="verified" <?php echo isset($_GET['verification']) && $_GET['verification'] === 'verified' ? 'selected' : ''; ?>>Verified</option>
                            <option value="unverified" <?php echo isset($_GET['verification']) && $_GET['verification'] === 'unverified' ? 'selected' : ''; ?>>Unverified</option>
                        </select>
                    </div>
                </div>
                <?php if ($isSuperAdmin && !empty($staffMembers)): ?>
                <div class="col-lg-2 col-md-3 col-sm-6">
                    <div class="form-group mb-2">
                        <label for="staff_id" class="form-label">Staff</label>
                        <select class="form-select" id="staff_id" name="staff_id">
                            <option value="">All</option>
                            <?php foreach ($staffMembers as $staff): ?>
                            <option value="<?php echo $staff['id']; ?>" <?php echo $staffId == $staff['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($staff['name']); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                <?php endif; ?>
                <div class="col-lg-2 col-md-4 col-sm-6">
                    <div class="form-group mb-2">
                        <label for="date_range" class="form-label">Date</label>
                        <select class="form-select" id="date_range" name="date_range">
                            <option value="">All</option>
                            <option value="today" <?php echo isset($_GET['date_range']) && $_GET['date_range'] === 'today' ? 'selected' : ''; ?>>Today</option>
                            <option value="week" <?php echo isset($_GET['date_range']) && $_GET['date_range'] === 'week' ? 'selected' : ''; ?>>This Week</option>
                            <option value="month" <?php echo isset($_GET['date_range']) && $_GET['date_range'] === 'month' ? 'selected' : ''; ?>>This Month</option>
                            <option value="year" <?php echo isset($_GET['date_range']) && $_GET['date_range'] === 'year' ? 'selected' : ''; ?>>This Year</option>
                        </select>
                    </div>
                </div>
                <div class="col-lg-2 col-md-4 col-sm-6">
                    <div class="form-group mb-2">
                        <label for="sort" class="form-label">Sort</label>
                        <div class="input-group">
                            <select class="form-select border-end-0" id="sort" name="sort">
                                <option value="created_at" <?php echo $sort === 'created_at' ? 'selected' : ''; ?>>Date</option>
                                <option value="name" <?php echo $sort === 'name' ? 'selected' : ''; ?>>Name</option>
                                <option value="username" <?php echo $sort === 'username' ? 'selected' : ''; ?>>Username</option>
                                <option value="phone_number" <?php echo $sort === 'phone_number' ? 'selected' : ''; ?>>Phone</option>
                                <option value="last_login" <?php echo $sort === 'last_login' ? 'selected' : ''; ?>>Last Login</option>
                            </select>
                            <select class="form-select border-start-0 ps-0 bg-light" id="order" name="order" style="max-width: 60px;">
                                <option value="ASC" <?php echo $order === 'ASC' ? 'selected' : ''; ?>><i class="fas fa-sort-up"></i></option>
                                <option value="DESC" <?php echo $order === 'DESC' ? 'selected' : ''; ?>><i class="fas fa-sort-down"></i></option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-12 d-flex justify-content-end mt-2">
                    <a href="users.php" class="btn btn-success btn-sm dashboard-green-btn me-2">
                        <i class="fas fa-redo me-1"></i> Reset
                    </a>
                    <button type="submit" class="btn btn-success btn-sm dashboard-green-btn">
                        <i class="fas fa-filter me-1"></i> Apply
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Users Table -->
<form action="users.php" method="post" id="usersForm">
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white py-3">
            <div class="d-flex flex-column flex-sm-row align-items-sm-center justify-content-between gap-2">
                <div class="d-flex align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary mb-0">App Users List</h6>
                    <span class="badge bg-primary rounded-pill ms-2"><?php echo number_format($totalUsers); ?> users</span>
                </div>

            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0" id="usersTable">
                    <thead class="bg-light">
                        <tr>
                            <th>User</th>
                            <th>Phone Number</th>
                            <th>Registered</th>
                            <th>Last Login</th>
                            <th class="text-end pe-3">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (!empty($users)): ?>
                            <?php foreach ($users as $user):
                                // Calculate BMI if height and weight are available
                                $bmi = null;
                                if (!empty($user['height']) && !empty($user['weight'])) {
                                    $heightInMeters = $user['height'] / 100;
                                    $bmi = number_format($user['weight'] / ($heightInMeters * $heightInMeters), 1);
                                }
                                // Fetch the first enrolled course for this user
                                $courseId = null;
                                $courseQuery = "SELECT course_id FROM user_course_enrollments WHERE user_id = ? ORDER BY start_date ASC LIMIT 1";
                                $courseStmt = $conn->prepare($courseQuery);
                                $courseStmt->bind_param("i", $user['id']);
                                $courseStmt->execute();
                                $courseResult = $courseStmt->get_result();
                                if ($row = $courseResult->fetch_assoc()) {
                                    $courseId = $row['course_id'];
                                }
                            ?>
                                <tr>
                                    <td data-label="User">
                                        <div class="d-flex align-items-center">
                                            <?php
                                              $initials = strtoupper(substr($user['name'] ?? 'U', 0, 1));
                                              $bgColor = '#28a745';
                                              $textColor = '#ffffff';
                                              $avatarSize = '38px';
                                            ?>
                                            <div class="user-avatar-initials"
                                                 style="width:<?= $avatarSize ?>;
                                                        height:<?= $avatarSize ?>;
                                                        background-color:<?= $bgColor ?>;
                                                        color:<?= $textColor ?>;
                                                        border-radius:50%;
                                                        display:inline-flex;
                                                        align-items:center;
                                                        justify-content:center;
                                                        margin-right:12px;
                                                        font-weight:600;
                                                        font-size:15px;
                                                        border:1px solid #218838;
                                                        box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                                                <?= htmlspecialchars($initials) ?>
                                            </div>
                                            <div class="d-flex flex-column">
                                              <div class="fw-medium text-dark"><?php echo htmlspecialchars($user['name']); ?></div>
                                              <div class="d-flex align-items-center">
                                                <small class="text-muted me-2">@<?php echo htmlspecialchars($user['username']); ?></small>
                                                <?php if ($user['id'] == 1): ?>
                                                  <span class="badge bg-primary rounded-pill">Admin</span>
                                                <?php endif; ?>
                                                <?php if (!empty($user['email_verified_at'])): ?>
                                                  <span class="badge bg-success rounded-pill ms-1">Verified</span>
                                                <?php endif; ?>
                                              </div>
                                              <?php if ($isSuperAdmin && !empty($user['assigned_staff_id'])): ?>
                                                <?php
                                                // Get staff name
                                                $staffQuery = "SELECT name FROM admin_users WHERE id = ?";
                                                $staffStmt = $conn->prepare($staffQuery);
                                                $staffStmt->bind_param("i", $user['assigned_staff_id']);
                                                $staffStmt->execute();
                                                $staffResult = $staffStmt->get_result();
                                                if ($staffResult && $staffResult->num_rows > 0) {
                                                    $staffName = $staffResult->fetch_assoc()['name'];
                                                    echo '<div class="small" style="color:#27ae60;"><i class="fas fa-user-tag me-1"></i> ' . htmlspecialchars($staffName) . '</div>';
                                                }
                                                ?>
                                              <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td data-label="Phone Number">
                                        <?php
                                            // Show phone number as a single string, remove + and spaces if present
                                            $rawPhone = $user['phone_number'] ?? 'N/A';
                                            $displayPhone = preg_replace('/[^0-9]/', '', $rawPhone);
                                            echo $displayPhone ?: 'N/A';
                                        ?>
                                    </td>
                                    <td data-label="Registered">
                                        <div class="d-flex flex-column">
                                            <div class="small text-muted mb-1">Registered</div>
                                            <div class="d-flex align-items-center">
                                                <i class="far fa-calendar-alt text-muted me-2"></i>
                                                <?php
                                                if (!empty($user['created_at'])) {
                                                    $createdAt = strtotime($user['created_at']);
                                                    $now = time();
                                                    $daysAgo = floor(($now - $createdAt) / 86400);
                                                    echo $daysAgo === 0 ? 'Today' : ($daysAgo === 1 ? '1 day ago' : $daysAgo . ' days ago');
                                                } else {
                                                    echo '';
                                                }
                                                ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td data-label="Last Login">
                                        <div class="d-flex flex-column">
                                            <div class="small text-muted mb-1">Last Login</div>
                                            <?php if ($user['last_login']): ?>
                                                <div class="d-flex align-items-center">
                                                    <i class="far fa-clock text-muted me-2"></i>
                                                    <?php echo !empty($user['last_login']) ? date('M d, Y', strtotime($user['last_login'])) : 'Never'; ?>
                                                </div>
                                                <div class="small text-muted"><?php echo !empty($user['last_login']) ? date('h:i A', strtotime($user['last_login'])) : ''; ?></div>
                                            <?php else: ?>
                                                <span class="text-muted">Never</span>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td class="text-end pe-3" data-label="Actions">
                                        <div class="d-flex justify-content-end">
                                            <a href="user_view.php?id=<?php echo $user['id']; ?>" class="btn btn-sm btn-icon btn-success btn-rounded me-1 dashboard-green-btn" data-bs-toggle="tooltip" title="View Profile">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="user_edit.php?id=<?php echo $user['id']; ?>" class="btn btn-sm btn-icon btn-success btn-rounded me-1 dashboard-green-btn" data-bs-toggle="tooltip" title="Edit User">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <?php if ($isAdmin && $user['id'] != 1): ?>
                                            <a href="users.php?delete=<?php echo $user['id']; ?>" class="btn btn-sm btn-icon btn-danger btn-rounded dashboard-red-btn" data-bs-toggle="tooltip" title="Delete User" onclick="return confirm('Are you sure you want to delete this user? This action cannot be undone.')">
                                                <i class="fas fa-trash-alt"></i>
                                            </a>
                                            <?php endif; ?>
                                            <?php if ($courseId): ?>
                                            <a href="video_analytics.php?user_id=<?php echo $user['id']; ?>&course_id=<?php echo $courseId; ?>" class="btn btn-sm btn-icon btn-info btn-rounded ms-1" data-bs-toggle="tooltip" title="Analytics">
                                                <i class="fas fa-chart-bar"></i>
                                            </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="5" class="text-center py-5">
                                    <div class="empty-state">
                                        <div class="empty-state-icon">
                                            <i class="fas fa-users"></i>
                                        </div>
                                        <h5 class="mt-4">No App Users Found</h5>
                                        <p class="text-muted mb-4">No app users match your current search criteria</p>
                                        <a href="users.php" class="btn btn-success dashboard-green-btn">
                                            <i class="fas fa-redo me-2"></i> Clear All Filters
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</form>

<!-- Minimal Pagination Controls -->
<?php if ($totalPages > 1): ?>
    <nav class="mt-3 mb-4">
        <ul class="pagination pagination-sm mb-0" style="justify-content: flex-start;">
            <li class="page-item<?php if ($page <= 1) echo ' disabled'; ?>">
                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => max(1, $page - 1)])); ?>">&laquo; Prev</a>
            </li>
            <li class="page-item disabled">
                <span class="page-link" style="background: #f8f9fa; color: #222; border: none; min-width: 70px; text-align: center;">
                    Page <?php echo $page; ?> / <?php echo $totalPages; ?>
                </span>
            </li>
            <li class="page-item<?php if ($page >= $totalPages) echo ' disabled'; ?>">
                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => min($totalPages, $page + 1)])); ?>">Next &raquo;</a>
            </li>
        </ul>
    </nav>
<?php endif; ?>

<script>
// Remove DataTables initialization for usersTable (server-side pagination now)
</script>

<style>
/* Responsive Table Styles */
@media screen and (max-width: 767px) {
    .table-responsive {
        border: 0;
    }
    
    #usersTable {
        width: 100%;
    }
    
    #usersTable thead {
        display: none;
    }
    
    #usersTable tbody tr {
        display: block;
        margin-bottom: 1rem;
        border: none;
        border-radius: 16px;
        background: #fff;
        box-shadow: 0 2px 12px rgba(0,0,0,0.04);
        overflow: hidden;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }
    
    #usersTable tbody tr:active {
        transform: scale(0.98);
    }
    
    #usersTable tbody td {
    display: flex;
        justify-content: space-between;
    align-items: center;
        padding: 1.25rem;
        border: none;
        border-bottom: 1px solid #f8f9fa;
        background: #fff;
    }
    
    #usersTable tbody td:last-child {
        border-bottom: none;
    }
    
    #usersTable tbody td:before {
        content: attr(data-label);
        font-weight: 600;
    color: #6c757d;
        font-size: 0.813rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        margin-right: 1rem;
        min-width: 100px;
    }
    
    /* User Info Styling */
    #usersTable tbody td[data-label="User"] {
        padding: 1.5rem 1.25rem;
        background: #fff;
    }
    
    #usersTable tbody td[data-label="User"] .d-flex {
        width: 100%;
        flex-direction: row;
    align-items: center;
        gap: 1rem;
    }
    
    #usersTable tbody td[data-label="User"] .user-avatar-initials {
        margin: 0;
        flex-shrink: 0;
        box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        border: 2px solid #fff;
    }
    
    #usersTable tbody td[data-label="User"] .d-flex.flex-column {
        flex: 1;
    }
    
    #usersTable tbody td[data-label="User"] .fw-medium {
        font-size: 1.125rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.25rem;
        line-height: 1.3;
    }
    
    #usersTable tbody td[data-label="User"] .text-muted {
        font-size: 0.875rem;
        color: #6c757d;
    display: flex;
    align-items: center;
        gap: 0.5rem;
    }
    
    #usersTable tbody td[data-label="User"] .badge {
        font-size: 0.75rem;
        padding: 0.35em 0.65em;
        font-weight: 500;
        border-radius: 6px;
    }
    
    /* Phone Number Styling */
    #usersTable tbody td[data-label="Phone Number"] {
        font-size: 1rem;
        font-weight: 500;
        color: #2c3e50;
        padding: 1.25rem;
        background: #fff;
    }
    
    /* Actions Styling */
    #usersTable tbody td[data-label="Actions"] {
        padding: 1.25rem;
        background: #fff;
    }
    
    #usersTable tbody td[data-label="Actions"] .d-flex {
        justify-content: flex-end;
        gap: 0.75rem;
    }
    
    #usersTable tbody td[data-label="Actions"] .btn-icon {
        width: 44px;
        height: 44px;
        font-size: 1.1rem;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        box-shadow: 0 2px 8px rgba(0,0,0,0.06);
        border: none;
    }
    
    #usersTable tbody td[data-label="Actions"] .btn-icon:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }

    #usersTable tbody td[data-label="Actions"] .btn-icon.btn-success {
        background: #27ae60;
    }

    #usersTable tbody td[data-label="Actions"] .btn-icon.btn-success:hover {
        background: #219150;
    }
    
    /* Hide unnecessary elements on mobile */
    #usersTable tbody td[data-label="Registered"],
    #usersTable tbody td[data-label="Last Login"],
    #usersTable tbody td[data-label="Actions"] .action-menu-container {
        display: none;
    }

    /* Empty state styling */
    .empty-state {
        padding: 3rem 1.5rem;
        text-align: center;
        background: #fff;
        border-radius: 16px;
        box-shadow: 0 2px 12px rgba(0,0,0,0.04);
    }

    .empty-state-icon {
        font-size: 3rem;
        color: #e9ecef;
        margin-bottom: 1.5rem;
    }

    .empty-state h5 {
        font-size: 1.25rem;
        color: #2c3e50;
        margin-bottom: 0.75rem;
        font-weight: 600;
    }

    .empty-state p {
        color: #6c757d;
        margin-bottom: 1.75rem;
        font-size: 0.938rem;
    }

    .empty-state .btn {
        padding: 0.75rem 1.5rem;
        font-weight: 500;
        border-radius: 12px;
    }
}
</style>

<?php require_once 'includes/footer.php'; ?>